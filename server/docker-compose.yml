version: "3.8"

services:
  server:
    image: parameter-server-new
    container_name: parameter-server
    ports:
      - "${WEB_PORT}:${WEB_PORT}"
      - "${TCP_PORT}:${TCP_PORT}"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./static:/app/static
      - ./src:/app/src
      - ./.env:/app/.env
    environment:
      - TZ=${TIMEZONE}
      - WEB_PORT=${WEB_PORT}
      - TCP_PORT=${TCP_PORT}
      - SMS_ENABLED=${SMS_ENABLED}
      - SMS_ACCESS_KEY=${SMS_ACCESS_KEY}
      - SMS_SECRET_KEY=${SMS_SECRET_KEY}
      - SMS_PHONE_NUMBER=${SMS_PHONE_NUMBER}
      - SMS_TEMPLATE_CODE=${SMS_TEMPLATE_CODE}
      - SMS_SIGN_NAME=${SMS_SIGN_NAME}
      - LOG_LEVEL=${LOG_LEVEL}
      - LOG_MAX_SIZE_MB=${LOG_MAX_SIZE_MB}
      - LOG_BACKUP_COUNT=${LOG_BACKUP_COUNT}
      - DATA_DIR=${DATA_DIR}
      - CLIENT_DATA_FILE=${CLIENT_DATA_FILE}
      - TIMEZONE=${TIMEZONE}
    restart: always
    networks:
      - parameter-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${WEB_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  parameter-network:
    driver: bridge

#!/bin/bash

# 服务器启动脚本

set -e

echo "=========================================="
echo "参数设置系统服务器启动脚本"
echo "=========================================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装，请先安装Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装，请先安装docker-compose"
    exit 1
fi

# 进入脚本所在目录
cd "$(dirname "$0")"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p data logs static src

# 设置权限
chmod 755 data logs

# 检查配置文件
if [ ! -f ".env" ]; then
    echo "警告: .env文件不存在，将使用默认配置"
fi

# 停止现有容器（如果存在）
echo "停止现有容器..."
docker-compose down --remove-orphans || true

# 构建并启动服务
echo "构建Docker镜像..."
docker-compose build

echo "启动服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 显示日志
echo "显示最近的日志..."
docker-compose logs --tail=20

# 读取配置
WEB_PORT=$(grep "WEB_PORT=" .env | cut -d'=' -f2 | tr -d ' ')
TCP_PORT=$(grep "TCP_PORT=" .env | cut -d'=' -f2 | tr -d ' ')

echo "=========================================="
echo "服务器启动完成！"
echo ""
echo "Web界面: http://localhost:${WEB_PORT:-8000}"
echo "配置管理: http://localhost:${WEB_PORT:-8000}/config.html"
echo "日志管理: http://localhost:${WEB_PORT:-8000}/log-manager.html"
echo "TCP端口: ${TCP_PORT:-8888}"
echo "健康检查: http://localhost:${WEB_PORT:-8000}/health"
echo ""
echo "查看日志: docker-compose logs -f"
echo "停止服务: docker-compose down"
echo "=========================================="

#!/usr/bin/env python3
"""
服务器端配置管理器
支持.env文件和环境变量，实现配置的实时更新和热重载
参考客户端的配置管理实现
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from dotenv import load_dotenv


class ConfigManager:
    """配置管理器"""

    def __init__(self, env_file: str = ".env"):
        """
        初始化配置管理器

        Args:
            env_file: 环境变量文件路径
        """
        self.env_file = env_file
        self.logger = logging.getLogger(__name__)

        # 默认配置
        self.default_config = {
            'WEB_PORT': '8000',
            'TCP_PORT': '8888',
            # 钉钉通知配置
            'DINGTALK_ENABLED': 'false',
            'DINGTALK_WEBHOOK_URL': '',
            'DINGTALK_SECRET': '',
            'LOG_LEVEL': 'INFO',
            'LOG_MAX_SIZE_MB': '10',
            'LOG_BACKUP_COUNT': '10',
            'DATA_DIR': 'data',
            'CLIENT_DATA_FILE': 'clients.json',
            'TIMEZONE': 'Asia/Shanghai'
        }

        # 当前配置
        self.current_config = {}

        # 加载配置
        self.load_config()

    def load_config(self) -> Dict[str, str]:
        """加载配置（优先使用环境变量）"""
        # 首先加载.env文件
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)
            self.logger.info(f"已加载环境配置文件: {self.env_file}")

        # 从环境变量读取当前配置
        config = {}
        for key, default_value in self.default_config.items():
            config[key] = os.getenv(key, default_value)

        self.current_config = config

        self.logger.info("服务器配置加载成功:")
        self.logger.info(f"  Web端口: {config['WEB_PORT']}")
        self.logger.info(f"  TCP端口: {config['TCP_PORT']}")
        self.logger.info(f"  钉钉通知: {'启用' if config['DINGTALK_ENABLED'].lower() == 'true' else '禁用'}")
        self.logger.info(f"  日志级别: {config['LOG_LEVEL']}")

        return config

    def get_config(self) -> Dict[str, str]:
        """获取当前配置"""
        return self.current_config.copy()

    def get_config_value(self, key: str, default: str = None) -> str:
        """获取单个配置值"""
        return self.current_config.get(key, default or self.default_config.get(key, ''))

    def save_config(self, config: Dict[str, str]) -> bool:
        """保存配置到.env文件"""
        try:
            # 读取现有的.env文件内容
            existing_lines = []
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    existing_lines = f.readlines()

            # 更新配置
            updated_lines = []
            config_keys = set(config.keys())

            # 处理现有行
            for line in existing_lines:
                line = line.rstrip('\n')
                if '=' in line and not line.strip().startswith('#'):
                    key = line.split('=', 1)[0].strip()
                    if key in config:
                        # 更新现有配置
                        updated_lines.append(f"{key}={config[key]}")
                        config_keys.remove(key)
                    else:
                        # 保留其他配置
                        updated_lines.append(line)
                else:
                    # 保留注释和空行
                    updated_lines.append(line)

            # 添加新配置
            for key in config_keys:
                updated_lines.append(f"{key}={config[key]}")

            # 写入文件
            with open(self.env_file, 'w', encoding='utf-8') as f:
                for line in updated_lines:
                    f.write(line + '\n')

            self.logger.info(f"配置已保存到 {self.env_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            return False

    def reload_env_variables(self, config: Dict[str, str]):
        """重新加载环境变量"""
        for key, value in config.items():
            os.environ[key] = str(value)

        # 更新当前配置
        self.current_config.update(config)

        self.logger.info("环境变量已重新加载")

    def validate_config(self, config: Dict[str, str]) -> Dict[str, str]:
        """验证配置"""
        errors = {}

        # 验证端口号
        for port_key in ['WEB_PORT', 'TCP_PORT']:
            if port_key in config:
                try:
                    port = int(config[port_key])
                    if port < 1000 or port > 65535:
                        errors[port_key] = "端口号必须在1000-65535之间"
                except ValueError:
                    errors[port_key] = "端口号必须是数字"

        # 验证日志级别
        if 'LOG_LEVEL' in config:
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if config['LOG_LEVEL'] not in valid_levels:
                errors['LOG_LEVEL'] = f"日志级别必须是以下之一: {', '.join(valid_levels)}"

        # 验证日志配置
        if 'LOG_MAX_SIZE_MB' in config:
            try:
                size = float(config['LOG_MAX_SIZE_MB'])
                if size <= 0 or size > 1000:
                    errors['LOG_MAX_SIZE_MB'] = "日志文件最大大小必须在0-1000MB之间"
            except ValueError:
                errors['LOG_MAX_SIZE_MB'] = "日志文件最大大小必须是数字"

        if 'LOG_BACKUP_COUNT' in config:
            try:
                count = int(config['LOG_BACKUP_COUNT'])
                if count < 0 or count > 100:
                    errors['LOG_BACKUP_COUNT'] = "备份文件数量必须在0-100之间"
            except ValueError:
                errors['LOG_BACKUP_COUNT'] = "备份文件数量必须是整数"

        # 验证布尔值 - SMS相关配置已移除

        return errors

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "current_config": self.current_config,
            "default_config": self.default_config,
            "env_file": self.env_file,
            "last_updated": datetime.now().isoformat()
        }

    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            # 保存默认配置到.env文件
            success = self.save_config(self.default_config)
            if success:
                # 重新加载环境变量
                self.reload_env_variables(self.default_config)
                self.logger.info("配置已重置为默认值")
            return success
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            return False

    def update_config(self, config: Dict[str, str]) -> Dict[str, Any]:
        """更新配置"""
        try:
            # 验证配置
            errors = self.validate_config(config)
            if errors:
                return {
                    "success": False,
                    "message": "配置验证失败",
                    "errors": errors
                }

            # 保存配置
            success = self.save_config(config)
            if not success:
                return {
                    "success": False,
                    "message": "保存配置失败"
                }

            # 重新加载环境变量
            self.reload_env_variables(config)

            return {
                "success": True,
                "message": "配置更新成功",
                "data": self.get_config_info()
            }

        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            return {
                "success": False,
                "message": f"更新配置失败: {str(e)}"
            }

    def reload_from_file(self) -> bool:
        """从文件重新加载配置"""
        try:
            # 重新读取.env文件
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()

            # 重新加载配置
            self.load_config()
            self.logger.info("配置已从文件重新加载")
            return True

        except Exception as e:
            self.logger.error(f"从文件重新加载配置失败: {e}")
            return False


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def setup_config_manager(env_file: str = ".env") -> ConfigManager:
    """设置配置管理器"""
    global _config_manager
    _config_manager = ConfigManager(env_file)
    return _config_manager
#!/usr/bin/env python3
"""
短信服务
负责发送短信通知
"""

import json
import requests
import os
from typing import Optional

class SMSService:
    def __init__(self):
        self.enabled = os.getenv('SMS_ENABLED', 'false').lower() == 'true'
        self.access_key = os.getenv('SMS_ACCESS_KEY', '')
        self.access_secret = os.getenv('SMS_ACCESS_SECRET', '')
        self.sign_name = os.getenv('SMS_SIGN_NAME', '')
        self.template_code = os.getenv('SMS_TEMPLATE_CODE', '')
        self.phone_number = os.getenv('SMS_PHONE_NUMBER', '')

    def send_sms(self, message: str) -> bool:
        """发送短信通知"""
        if not self.enabled:
            print(f"短信服务未启用，消息: {message}")
            return True

        try:
            # 使用阿里云短信服务
            return self._send_aliyun_sms(message)

        except Exception as e:
            print(f"发送短信失败: {e}")
            return False

    def _send_aliyun_sms(self, message: str) -> bool:
        """发送阿里云短信"""
        try:
            # 这里是阿里云短信API的示例实现
            # 实际使用时需要安装阿里云SDK: pip install alibabacloud_dysmsapi20170525

            # 暂时只打印消息，不实际发送
            print(f"[阿里云短信] {message}")
            print(f"发送到: {self.phone_number}")

            # 实际实现示例（需要安装SDK）:
            """
            from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
            from alibabacloud_tea_openapi import models as open_api_models
            from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models

            config_sdk = open_api_models.Config(
                access_key_id=self.access_key,
                access_key_secret=self.access_secret,
            )
            config_sdk.endpoint = 'dysmsapi.aliyuncs.com'

            client = Dysmsapi20170525Client(config_sdk)
            send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
                phone_numbers=self.phone_number,
                sign_name=self.sign_name,
                template_code=self.template_code,
                template_param=json.dumps({"message": message})
            )

            response = client.send_sms(send_sms_request)
            return response.body.code == 'OK'
            """

            return True

        except Exception as e:
            print(f"发送阿里云短信失败: {e}")
            return False

    def send_client_online_notification(self, device_id: str, ip: str) -> bool:
        """发送客户端上线通知"""
        message = f"设备上线通知: {device_id} ({ip})"
        return self.send_sms(message)

    def send_client_offline_notification(self, device_id: str) -> bool:
        """发送客户端离线通知"""
        message = f"设备离线通知: {device_id}"
        return self.send_sms(message)

# 全局SMS服务实例
sms_service = SMSService()

def send_sms(message: str) -> bool:
    """发送短信的便捷函数"""
    return sms_service.send_sms(message)

def send_client_online_notification(device_id: str, ip: str) -> bool:
    """发送客户端上线通知的便捷函数"""
    return sms_service.send_client_online_notification(device_id, ip)

#!/usr/bin/env python3
"""
客户端连接日志管理器
专门记录客户端上线、下线等连接状态变化的日志
"""

import logging
import os
from datetime import datetime
from typing import Optional

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .log_manager import TimestampRotatingFileHandler
except ImportError:
    from log_manager import TimestampRotatingFileHandler


class ClientConnectionLogger:
    """客户端连接状态专用日志记录器"""

    def __init__(self, log_file: str = "logs/client_connections.log",
                 max_bytes: int = 50 * 1024 * 1024,  # 50MB
                 backup_count: int = 20):
        """
        初始化客户端连接日志记录器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 初始化日志记录器
        self.logger = self._setup_logger()

    def _setup_logger(self) -> logging.Logger:
        """设置专用的连接日志记录器"""
        logger_name = f"client_connection_{id(self)}"
        logger = logging.getLogger(logger_name)

        # 避免重复添加处理器
        if logger.handlers:
            return logger

        logger.setLevel(logging.INFO)

        # 创建文件处理器
        file_handler = TimestampRotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )

        # 设置日志格式 - 专门为连接日志优化
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)

        # 防止日志传播到根日志记录器
        logger.propagate = False

        return logger

    def log_client_online(self, client_id: str, client_ip: str,
                         web_port: int = None, server_type: str = None):
        """记录客户端上线"""
        extra_info = []
        if web_port:
            extra_info.append(f"Web端口:{web_port}")
        if server_type:
            extra_info.append(f"服务器类型:{server_type}")

        extra_str = f" ({', '.join(extra_info)})" if extra_info else ""

        self.logger.info(f"客户端上线 | ID:{client_id} | IP:{client_ip}{extra_str}")

    def log_client_offline(self, client_id: str, reason: str = "正常断开"):
        """记录客户端下线"""
        self.logger.info(f"客户端下线 | ID:{client_id} | 原因:{reason}")

    def log_client_replaced(self, old_client_id: str, new_client_id: str, client_ip: str):
        """记录客户端被替换（相同IP的新客户端连接）"""
        self.logger.warning(f"客户端替换 | 旧ID:{old_client_id} | 新ID:{new_client_id} | IP:{client_ip}")

    def log_heartbeat_timeout(self, client_id: str, last_heartbeat_time: str):
        """记录心跳超时"""
        self.logger.warning(f"心跳超时 | ID:{client_id} | 最后心跳:{last_heartbeat_time}")

    def log_connection_error(self, client_id: str, error_msg: str):
        """记录连接错误"""
        self.logger.error(f"连接错误 | ID:{client_id} | 错误:{error_msg}")

    def log_client_cleanup(self, client_ids: list, reason: str = "清理离线客户端"):
        """记录客户端清理操作"""
        if client_ids:
            self.logger.info(f"客户端清理 | 数量:{len(client_ids)} | 原因:{reason} | IDs:{','.join(client_ids)}")

    def log_duplicate_connection(self, client_id: str, client_ip: str):
        """记录重复连接尝试"""
        self.logger.warning(f"重复连接 | ID:{client_id} | IP:{client_ip}")


# 全局客户端连接日志记录器实例
_connection_logger: Optional[ClientConnectionLogger] = None


def get_connection_logger() -> ClientConnectionLogger:
    """获取全局客户端连接日志记录器实例"""
    global _connection_logger
    if _connection_logger is None:
        _connection_logger = ClientConnectionLogger()
    return _connection_logger


def setup_connection_logging(log_file: str = "logs/client_connections.log",
                           max_bytes: int = 50 * 1024 * 1024,
                           backup_count: int = 20) -> ClientConnectionLogger:
    """设置客户端连接日志系统"""
    global _connection_logger
    _connection_logger = ClientConnectionLogger(log_file, max_bytes, backup_count)
    return _connection_logger

#!/usr/bin/env python3
"""
服务器端简化日志管理器 - 只记录关键操作
专门记录：客户端连接、参数修改、服务器启动时间戳
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


class SimpleServerLogManager:
    """简化的服务器日志管理器 - 只记录关键操作"""

    def __init__(self, log_file: str = "logs/server_operations.log",
                 max_bytes: int = 5 * 1024 * 1024,  # 5MB
                 backup_count: int = 5):
        """
        初始化简化服务器日志管理器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 设置简化的日志格式 - 只包含时间戳和消息
        self.formatter = logging.Formatter('%(asctime)s | %(message)s')

        # 初始化日志记录器
        self.logger = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        # 创建专用的日志记录器
        self.logger = logging.getLogger('server_operations_logger')
        self.logger.setLevel(logging.INFO)

        # 清除现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        self.logger.addHandler(file_handler)

        # 不添加控制台处理器，保持日志简洁

    def log_server_startup(self, server_port: int, client_port: int = None):
        """记录服务器启动"""
        if client_port:
            self.logger.info(f"服务器启动 | Web端口: {server_port} | 客户端端口: {client_port}")
        else:
            self.logger.info(f"服务器启动 | Web端口: {server_port}")

    def log_client_connect(self, client_id: str, client_ip: str, web_port: int = None):
        """记录客户端连接"""
        if web_port:
            self.logger.info(f"客户端连接 | ID: {client_id} | IP: {client_ip} | Web端口: {web_port}")
        else:
            self.logger.info(f"客户端连接 | ID: {client_id} | IP: {client_ip}")

    def log_client_disconnect(self, client_id: str, reason: str = "正常断开"):
        """记录客户端断开"""
        self.logger.info(f"客户端断开 | ID: {client_id} | 原因: {reason}")

    def log_parameter_request(self, client_id: str, operation: str, param_info: str = None):
        """记录参数操作请求"""
        if param_info:
            self.logger.info(f"参数操作 | 客户端: {client_id} | 操作: {operation} | 参数: {param_info}")
        else:
            self.logger.info(f"参数操作 | 客户端: {client_id} | 操作: {operation}")

    def get_log_info(self) -> dict:
        """获取日志信息"""
        log_info = {
            "current_log_file": self.log_file,
            "max_size_mb": self.max_bytes / 1024 / 1024,
            "backup_count": self.backup_count
        }

        # 获取当前日志文件大小
        if os.path.exists(self.log_file):
            current_size = os.path.getsize(self.log_file)
            log_info["current_size_mb"] = current_size / 1024 / 1024
            log_info["usage_percent"] = (current_size / self.max_bytes) * 100
        else:
            log_info["current_size_mb"] = 0
            log_info["usage_percent"] = 0

        return log_info


class TimestampRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """带时间戳的轮转文件处理器"""

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        """
        初始化轮转文件处理器

        Args:
            filename: 日志文件名
            mode: 文件打开模式
            maxBytes: 最大文件大小（字节），0表示不限制
            backupCount: 保留的备份文件数量
            encoding: 文件编码
            delay: 是否延迟创建文件
        """
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)

    def doRollover(self):
        """执行日志轮转"""
        if self.stream:
            self.stream.close()
            self.stream = None

        # 生成带时间戳的备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = os.path.splitext(self.baseFilename)[0]
        extension = os.path.splitext(self.baseFilename)[1]

        # 新的备份文件名格式: server_20250730_143022.log
        backup_filename = f"{base_filename}_{timestamp}{extension}"

        # 移动当前日志文件到备份文件
        if os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, backup_filename)

        # 清理旧的备份文件（如果设置了backupCount）
        if self.backupCount > 0:
            self._cleanup_old_backups()

        # 重新打开日志文件
        if not self.delay:
            self.stream = self._open()

    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            # 获取日志文件目录
            log_dir = os.path.dirname(self.baseFilename)
            base_name = os.path.splitext(os.path.basename(self.baseFilename))[0]
            extension = os.path.splitext(self.baseFilename)[1]

            # 查找所有备份文件
            backup_files = []
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append((full_path, os.path.getmtime(full_path)))

            # 按修改时间排序，保留最新的backupCount个文件
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除多余的备份文件
            for file_path, _ in backup_files[self.backupCount:]:
                try:
                    os.remove(file_path)
                    print(f"删除旧日志文件: {file_path}")
                except OSError as e:
                    print(f"删除日志文件失败 {file_path}: {e}")

        except Exception as e:
            print(f"清理备份文件时出错: {e}")


class LogManager:
    """日志管理器"""

    def __init__(self, log_file: str = "logs/server.log",
                 max_bytes: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 10,
                 log_level: str = "INFO"):
        """
        初始化日志管理器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
            log_level: 日志级别
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count
        self.log_level = log_level

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 设置日志格式
        self.formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 初始化日志记录器
        self.logger = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建轮转文件处理器
        file_handler = TimestampRotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)

        # 配置根日志记录器
        root_logger.setLevel(getattr(logging, self.log_level.upper()))
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"服务器日志管理器初始化完成 - 文件: {self.log_file}, 最大大小: {self.max_bytes/1024/1024:.1f}MB, 备份数量: {self.backup_count}")

    def get_logger(self, name: str = None) -> logging.Logger:
        """获取日志记录器"""
        return logging.getLogger(name) if name else self.logger

    def get_log_info(self) -> dict:
        """获取日志信息"""
        log_info = {
            "current_log_file": self.log_file,
            "max_size_mb": self.max_bytes / 1024 / 1024,
            "backup_count": self.backup_count,
            "log_level": self.log_level
        }

        # 获取当前日志文件大小
        if os.path.exists(self.log_file):
            current_size = os.path.getsize(self.log_file)
            log_info["current_size_mb"] = current_size / 1024 / 1024
            log_info["usage_percent"] = (current_size / self.max_bytes) * 100
        else:
            log_info["current_size_mb"] = 0
            log_info["usage_percent"] = 0

        # 获取备份文件列表
        log_dir = os.path.dirname(self.log_file)
        base_name = os.path.splitext(os.path.basename(self.log_file))[0]
        extension = os.path.splitext(self.log_file)[1]

        backup_files = []
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append({
                            "filename": filename,
                            "size_mb": os.path.getsize(full_path) / 1024 / 1024,
                            "modified_time": datetime.fromtimestamp(os.path.getmtime(full_path)).strftime("%Y-%m-%d %H:%M:%S")
                        })

        # 按修改时间排序
        backup_files.sort(key=lambda x: x["modified_time"], reverse=True)
        log_info["backup_files"] = backup_files

        return log_info

    def force_rotate(self):
        """强制执行日志轮转"""
        for handler in logging.getLogger().handlers:
            if isinstance(handler, TimestampRotatingFileHandler):
                handler.doRollover()
                self.logger.info("手动执行日志轮转")
                break

    def update_config(self, max_size_mb=None, backup_count=None, log_level=None):
        """更新日志配置"""
        try:
            updated = False

            # 更新最大文件大小
            if max_size_mb is not None:
                new_max_bytes = int(max_size_mb * 1024 * 1024)
                if new_max_bytes != self.max_bytes:
                    self.max_bytes = new_max_bytes
                    updated = True
                    self.logger.info(f"更新最大文件大小: {max_size_mb}MB")

            # 更新备份文件数量
            if backup_count is not None:
                if backup_count != self.backup_count:
                    self.backup_count = backup_count
                    updated = True
                    self.logger.info(f"更新备份文件数量: {backup_count}")

            # 更新日志级别
            if log_level is not None:
                if log_level != self.log_level:
                    self.log_level = log_level
                    # 更新根日志记录器的级别
                    logging.getLogger().setLevel(getattr(logging, log_level.upper()))
                    updated = True
                    self.logger.info(f"更新日志级别: {log_level}")

            # 如果有更新，重新配置处理器
            if updated:
                self._update_handlers()

            return updated

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志配置失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def _update_handlers(self):
        """更新日志处理器配置"""
        try:
            root_logger = logging.getLogger()
            self.logger.info(f"开始更新日志处理器，当前处理器数量: {len(root_logger.handlers)}")

            # 找到并更新文件处理器
            handler_found = False
            for i, handler in enumerate(root_logger.handlers):
                self.logger.info(f"处理器 {i}: {type(handler).__name__}")
                if isinstance(handler, TimestampRotatingFileHandler):
                    old_max_bytes = handler.maxBytes
                    old_backup_count = handler.backupCount
                    handler.maxBytes = self.max_bytes
                    handler.backupCount = self.backup_count
                    self.logger.info(f"日志处理器配置已更新: maxBytes {old_max_bytes} -> {self.max_bytes}, backupCount {old_backup_count} -> {self.backup_count}")
                    handler_found = True
                    break

            if not handler_found:
                self.logger.warning("未找到TimestampRotatingFileHandler处理器")

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志处理器失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")


# 全局日志管理器实例
_log_manager: Optional[LogManager] = None
_simple_server_log_manager: Optional[SimpleServerLogManager] = None


def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        # 如果没有通过setup_logging初始化，使用默认配置
        _log_manager = LogManager()
    return _log_manager


def get_simple_server_log_manager() -> SimpleServerLogManager:
    """获取简化服务器日志管理器实例"""
    global _simple_server_log_manager
    if _simple_server_log_manager is None:
        _simple_server_log_manager = SimpleServerLogManager()
    return _simple_server_log_manager


def setup_logging(log_file: str = "logs/server.log",
                 max_bytes: int = 10 * 1024 * 1024,
                 backup_count: int = 10,
                 log_level: str = "INFO") -> LogManager:
    """设置日志系统"""
    global _log_manager
    _log_manager = LogManager(log_file, max_bytes, backup_count, log_level)
    return _log_manager


def setup_simple_server_logging(log_file: str = "logs/server_operations.log",
                               max_bytes: int = 5 * 1024 * 1024,
                               backup_count: int = 5) -> SimpleServerLogManager:
    """设置简化服务器日志系统"""
    global _simple_server_log_manager
    _simple_server_log_manager = SimpleServerLogManager(log_file, max_bytes, backup_count)
    return _simple_server_log_manager
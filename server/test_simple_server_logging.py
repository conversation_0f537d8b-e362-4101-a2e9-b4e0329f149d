#!/usr/bin/env python3
"""
测试服务器端简化日志管理器功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from log_manager import SimpleServerLogManager, setup_simple_server_logging, get_simple_server_log_manager


def test_simple_server_logging():
    """测试简化服务器日志管理器"""
    print("测试简化服务器日志管理器...")
    
    # 使用临时目录
    import tempfile
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "test_server_operations.log")
    
    # 初始化简化服务器日志管理器
    simple_logger = setup_simple_server_logging(log_file)
    
    # 测试服务器启动记录
    print("1. 测试服务器启动记录")
    simple_logger.log_server_startup(8080, 9999)
    simple_logger.log_server_startup(8080)  # 不带客户端端口
    
    # 测试客户端连接记录
    print("2. 测试客户端连接记录")
    simple_logger.log_client_connect("RK3588_001", "192.168.1.100", 7001)
    simple_logger.log_client_connect("RK3588_002", "192.168.1.101")  # 不带Web端口
    
    # 测试客户端断开记录
    print("3. 测试客户端断开记录")
    simple_logger.log_client_disconnect("RK3588_001", "正常断开")
    simple_logger.log_client_disconnect("RK3588_002", "网络超时")
    
    # 测试参数操作记录
    print("4. 测试参数操作记录")
    simple_logger.log_parameter_request("RK3588_001", "读取参数", "temperature_setpoint")
    simple_logger.log_parameter_request("RK3588_001", "写入参数", "pressure_limit: 100.5 -> 120.8")
    simple_logger.log_parameter_request("RK3588_002", "批量读取")
    
    # 获取日志信息
    print("5. 获取日志信息")
    log_info = simple_logger.get_log_info()
    print(f"日志文件: {log_info['current_log_file']}")
    print(f"文件大小: {log_info['current_size_mb']:.3f} MB")
    print(f"使用率: {log_info['usage_percent']:.1f}%")
    
    print(f"\n测试完成！请查看日志文件: {log_file}")
    
    # 显示日志文件内容
    if os.path.exists(log_file):
        print("\n=== 日志文件内容 ===")
        with open(log_file, 'r', encoding='utf-8') as f:
            print(f.read())
    
    # 清理临时文件
    import shutil
    shutil.rmtree(temp_dir)


if __name__ == "__main__":
    test_simple_server_logging()

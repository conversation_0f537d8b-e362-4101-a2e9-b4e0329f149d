#!/usr/bin/env python3
"""
简化的日志管理器 - 只记录关键操作
专门记录：用户登录、参数修改、客户端启动时间戳
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


class SimpleLogManager:
    """简化的日志管理器 - 只记录关键操作"""

    def __init__(self, log_file: str = "logs/client_operations.log",
                 max_bytes: int = 5 * 1024 * 1024,  # 5MB
                 backup_count: int = 5):
        """
        初始化简化日志管理器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 设置简化的日志格式 - 只包含时间戳和消息
        self.formatter = logging.Formatter('%(asctime)s | %(message)s')

        # 初始化日志记录器
        self.logger = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        # 创建专用的日志记录器
        self.logger = logging.getLogger('operations_logger')
        self.logger.setLevel(logging.INFO)

        # 清除现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 创建轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        self.logger.addHandler(file_handler)

        # 不添加控制台处理器，保持日志简洁

    def log_client_startup(self, device_id: str, web_port: int):
        """记录客户端启动"""
        self.logger.info(f"客户端启动 | 设备ID: {device_id} | Web端口: {web_port}")

    def log_user_login(self, user_ip: str, user_agent: str = None):
        """记录用户登录客户端"""
        if user_agent:
            self.logger.info(f"用户登录 | IP: {user_ip} | 浏览器: {user_agent}")
        else:
            self.logger.info(f"用户登录 | IP: {user_ip}")

    def log_parameter_change(self, user_ip: str, param_name: str, old_value: float, new_value: float):
        """记录参数修改"""
        self.logger.info(f"参数修改 | 用户IP: {user_ip} | 参数: {param_name} | 修改前: {old_value} | 修改后: {new_value}")

    def log_batch_parameter_change(self, user_ip: str, changes: list):
        """记录批量参数修改"""
        for change in changes:
            param_name = change.get('name')
            old_value = change.get('old_value')
            new_value = change.get('new_value')
            self.logger.info(f"批量参数修改 | 用户IP: {user_ip} | 参数: {param_name} | 修改前: {old_value} | 修改后: {new_value}")

    def get_log_info(self) -> dict:
        """获取日志信息"""
        log_info = {
            "current_log_file": self.log_file,
            "max_size_mb": self.max_bytes / 1024 / 1024,
            "backup_count": self.backup_count
        }

        # 获取当前日志文件大小
        if os.path.exists(self.log_file):
            current_size = os.path.getsize(self.log_file)
            log_info["current_size_mb"] = current_size / 1024 / 1024
            log_info["usage_percent"] = (current_size / self.max_bytes) * 100
        else:
            log_info["current_size_mb"] = 0
            log_info["usage_percent"] = 0

        return log_info


class TimestampRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """带时间戳的轮转文件处理器"""

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        """
        初始化轮转文件处理器

        Args:
            filename: 日志文件名
            mode: 文件打开模式
            maxBytes: 最大文件大小（字节），0表示不限制
            backupCount: 保留的备份文件数量
            encoding: 文件编码
            delay: 是否延迟创建文件
        """
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)

    def doRollover(self):
        """执行日志轮转"""
        if self.stream:
            self.stream.close()
            self.stream = None

        # 生成带时间戳的备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = os.path.splitext(self.baseFilename)[0]
        extension = os.path.splitext(self.baseFilename)[1]

        # 新的备份文件名格式: client_20250730_143022.log
        backup_filename = f"{base_filename}_{timestamp}{extension}"

        # 移动当前日志文件到备份文件
        if os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, backup_filename)

        # 清理旧的备份文件（如果设置了backupCount）
        if self.backupCount > 0:
            self._cleanup_old_backups()

        # 重新打开日志文件
        if not self.delay:
            self.stream = self._open()

    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            # 获取日志文件目录
            log_dir = os.path.dirname(self.baseFilename)
            base_name = os.path.splitext(os.path.basename(self.baseFilename))[0]
            extension = os.path.splitext(self.baseFilename)[1]

            # 查找所有备份文件
            backup_files = []
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append((full_path, os.path.getmtime(full_path)))

            # 按修改时间排序，保留最新的backupCount个文件
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除多余的备份文件
            for file_path, _ in backup_files[self.backupCount:]:
                try:
                    os.remove(file_path)
                    print(f"删除旧日志文件: {file_path}")
                except OSError as e:
                    print(f"删除日志文件失败 {file_path}: {e}")

        except Exception as e:
            print(f"清理备份文件时出错: {e}")


class LogManager:
    """原有的完整日志管理器 - 保持兼容性"""

    def __init__(self, log_file: str = "logs/client.log",
                 max_bytes: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 10,
                 log_level: str = "INFO"):
        """
        初始化日志管理器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
            log_level: 日志级别
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count
        self.log_level = log_level

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 设置日志格式
        self.formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 初始化日志记录器
        self.logger = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        # 清除现有的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建轮转文件处理器
        file_handler = TimestampRotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)

        # 配置根日志记录器
        root_logger.setLevel(getattr(logging, self.log_level.upper()))
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志管理器初始化完成 - 文件: {self.log_file}, 最大大小: {self.max_bytes/1024/1024:.1f}MB, 备份数量: {self.backup_count}")

    def get_logger(self, name: str = None) -> logging.Logger:
        """获取日志记录器"""
        return logging.getLogger(name) if name else self.logger

    def get_log_info(self) -> dict:
        """获取日志信息"""
        log_info = {
            "current_log_file": self.log_file,
            "max_size_mb": self.max_bytes / 1024 / 1024,
            "backup_count": self.backup_count,
            "log_level": self.log_level
        }

        # 获取当前日志文件大小
        if os.path.exists(self.log_file):
            current_size = os.path.getsize(self.log_file)
            log_info["current_size_mb"] = current_size / 1024 / 1024
            log_info["usage_percent"] = (current_size / self.max_bytes) * 100
        else:
            log_info["current_size_mb"] = 0
            log_info["usage_percent"] = 0

        # 获取备份文件列表
        log_dir = os.path.dirname(self.log_file)
        base_name = os.path.splitext(os.path.basename(self.log_file))[0]
        extension = os.path.splitext(self.log_file)[1]

        backup_files = []
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append({
                            "filename": filename,
                            "size_mb": os.path.getsize(full_path) / 1024 / 1024,
                            "modified_time": datetime.fromtimestamp(os.path.getmtime(full_path)).strftime("%Y-%m-%d %H:%M:%S")
                        })

        # 按修改时间排序
        backup_files.sort(key=lambda x: x["modified_time"], reverse=True)
        log_info["backup_files"] = backup_files

        return log_info

    def force_rotate(self):
        """强制执行日志轮转"""
        for handler in logging.getLogger().handlers:
            if isinstance(handler, TimestampRotatingFileHandler):
                handler.doRollover()
                self.logger.info("手动执行日志轮转")
                break

    def update_config(self, max_size_mb=None, backup_count=None, log_level=None):
        """更新日志配置"""
        try:
            updated = False

            # 更新最大文件大小
            if max_size_mb is not None:
                new_max_bytes = int(max_size_mb * 1024 * 1024)
                if new_max_bytes != self.max_bytes:
                    self.max_bytes = new_max_bytes
                    updated = True
                    self.logger.info(f"更新最大文件大小: {max_size_mb}MB")

            # 更新备份文件数量
            if backup_count is not None:
                if backup_count != self.backup_count:
                    self.backup_count = backup_count
                    updated = True
                    self.logger.info(f"更新备份文件数量: {backup_count}")

            # 更新日志级别
            if log_level is not None:
                if log_level != self.log_level:
                    self.log_level = log_level
                    # 更新根日志记录器的级别
                    logging.getLogger().setLevel(getattr(logging, log_level.upper()))
                    updated = True
                    self.logger.info(f"更新日志级别: {log_level}")

            # 如果有更新，重新配置处理器
            if updated:
                self._update_handlers()

            return updated

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志配置失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def _update_handlers(self):
        """更新日志处理器配置"""
        try:
            root_logger = logging.getLogger()
            self.logger.info(f"开始更新日志处理器，当前处理器数量: {len(root_logger.handlers)}")

            # 找到并更新文件处理器
            handler_found = False
            for i, handler in enumerate(root_logger.handlers):
                self.logger.info(f"处理器 {i}: {type(handler).__name__}")
                if isinstance(handler, TimestampRotatingFileHandler):
                    old_max_bytes = handler.maxBytes
                    old_backup_count = handler.backupCount
                    handler.maxBytes = self.max_bytes
                    handler.backupCount = self.backup_count
                    self.logger.info(f"日志处理器配置已更新: maxBytes {old_max_bytes} -> {self.max_bytes}, backupCount {old_backup_count} -> {self.backup_count}")
                    handler_found = True
                    break

            if not handler_found:
                self.logger.warning("未找到TimestampRotatingFileHandler处理器")

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志处理器失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")


# 全局日志管理器实例
_log_manager: Optional[LogManager] = None
_simple_log_manager: Optional[SimpleLogManager] = None


def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        # 如果没有通过setup_logging初始化，使用默认配置
        _log_manager = LogManager()
    return _log_manager


def get_simple_log_manager() -> SimpleLogManager:
    """获取简化日志管理器实例"""
    global _simple_log_manager
    if _simple_log_manager is None:
        _simple_log_manager = SimpleLogManager()
    return _simple_log_manager


def setup_logging(log_file: str = "logs/client.log",
                 max_bytes: int = 10 * 1024 * 1024,
                 backup_count: int = 10,
                 log_level: str = "INFO") -> LogManager:
    """设置日志系统"""
    global _log_manager
    _log_manager = LogManager(log_file, max_bytes, backup_count, log_level)
    return _log_manager


def setup_simple_logging(log_file: str = "logs/client_operations.log",
                        max_bytes: int = 5 * 1024 * 1024,
                        backup_count: int = 5) -> SimpleLogManager:
    """设置简化日志系统"""
    global _simple_log_manager
    _simple_log_manager = SimpleLogManager(log_file, max_bytes, backup_count)
    return _simple_log_manager

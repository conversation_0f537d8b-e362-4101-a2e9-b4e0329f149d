# 简化日志管理器说明

## 概述

为了简化日志记录内容，我们新增了一个专门的简化日志管理器 `SimpleLogManager`，只记录关键操作信息：

1. **用户登录客户端**
2. **参数修改操作**（包括修改前后的参数名称和值）
3. **客户端启动时间戳**

## 功能特点

### 1. 简洁的日志格式
- 只包含时间戳和操作信息
- 格式：`时间戳 | 操作描述`
- 不包含日志级别、模块名等冗余信息

### 2. 记录的操作类型

#### 客户端启动
```
2025-08-02 15:43:45,943 | 客户端启动 | 设备ID: RK3588_001 | Web端口: 7001
```

#### 用户登录
```
2025-08-02 15:43:45,943 | 用户登录 | IP: ************* | 浏览器: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
2025-08-02 15:43:45,943 | 用户登录 | IP: *************
```

#### 单个参数修改
```
2025-08-02 15:43:45,943 | 参数修改 | 用户IP: ************* | 参数: temperature_setpoint | 修改前: 25.0 | 修改后: 30.0
```

#### 批量参数修改
```
2025-08-02 15:43:45,943 | 批量参数修改 | 用户IP: ************* | 参数: param1 | 修改前: 10.0 | 修改后: 15.0
2025-08-02 15:43:45,943 | 批量参数修改 | 用户IP: ************* | 参数: param2 | 修改前: 20.0 | 修改后: 25.0
```

### 3. 文件管理
- 默认日志文件：`logs/client_operations.log`
- 文件大小限制：5MB（可配置）
- 备份文件数量：5个（可配置）
- 自动轮转：当文件超过大小限制时自动创建备份

## 使用方法

### 1. 初始化简化日志管理器

```python
from log_manager import setup_simple_logging, get_simple_log_manager

# 设置简化日志系统
setup_simple_logging()

# 获取简化日志管理器实例
simple_logger = get_simple_log_manager()
```

### 2. 记录操作

```python
# 记录客户端启动
simple_logger.log_client_startup("RK3588_001", 7001)

# 记录用户登录
simple_logger.log_user_login("*************", "Mozilla/5.0...")

# 记录参数修改
simple_logger.log_parameter_change("*************", "temp_setpoint", 25.0, 30.0)

# 记录批量参数修改
changes = [
    {'name': 'param1', 'old_value': 10.0, 'new_value': 15.0},
    {'name': 'param2', 'old_value': 20.0, 'new_value': 25.0}
]
simple_logger.log_batch_parameter_change("*************", changes)
```

## 与原有日志系统的关系

- **原有日志系统**：保持不变，继续记录详细的系统运行信息、错误信息等
- **简化日志系统**：专门记录用户操作和关键事件，便于审计和追踪

两个系统并行运行，互不干扰。

## 配置选项

```python
# 自定义配置
setup_simple_logging(
    log_file="logs/my_operations.log",  # 自定义日志文件路径
    max_bytes=10 * 1024 * 1024,        # 10MB文件大小限制
    backup_count=10                     # 保留10个备份文件
)
```

## 测试

运行测试脚本验证功能：

```bash
python test_simple_logging.py
```

## 集成状态

简化日志管理器已经集成到以下模块：

1. **main.py** - 记录客户端启动
2. **web_server.py** - 记录用户登录和参数修改
3. **log_manager.py** - 提供简化日志管理功能

所有关键用户操作都会自动记录到简化日志中。

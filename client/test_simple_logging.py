#!/usr/bin/env python3
"""
测试简化日志管理器功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from log_manager import SimpleLogManager, setup_simple_logging, get_simple_log_manager


def test_simple_logging():
    """测试简化日志管理器"""
    print("测试简化日志管理器...")

    # 使用临时目录
    import tempfile
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "test_operations.log")

    # 初始化简化日志管理器
    simple_logger = setup_simple_logging(log_file)

    # 测试客户端启动记录
    print("1. 测试客户端启动记录")
    simple_logger.log_client_startup("RK3588_TEST_001", 7001)

    # 测试用户登录记录
    print("2. 测试用户登录记录")
    simple_logger.log_user_login("192.168.1.100", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    simple_logger.log_user_login("192.168.1.101")  # 不带浏览器信息

    # 测试参数修改记录
    print("3. 测试参数修改记录")
    simple_logger.log_parameter_change("192.168.1.100", "temperature_setpoint", 25.0, 30.0)
    simple_logger.log_parameter_change("192.168.1.100", "pressure_limit", 100.5, 120.8)

    # 测试批量参数修改记录
    print("4. 测试批量参数修改记录")
    changes = [
        {'name': 'param1', 'old_value': 10.0, 'new_value': 15.0},
        {'name': 'param2', 'old_value': 20.0, 'new_value': 25.0},
        {'name': 'param3', 'old_value': 30.0, 'new_value': 35.0}
    ]
    simple_logger.log_batch_parameter_change("192.168.1.102", changes)

    # 获取日志信息
    print("5. 获取日志信息")
    log_info = simple_logger.get_log_info()
    print(f"日志文件: {log_info['current_log_file']}")
    print(f"文件大小: {log_info['current_size_mb']:.3f} MB")
    print(f"使用率: {log_info['usage_percent']:.1f}%")

    print(f"\n测试完成！请查看日志文件: {log_file}")

    # 显示日志文件内容
    if os.path.exists(log_file):
        print("\n=== 日志文件内容 ===")
        with open(log_file, 'r', encoding='utf-8') as f:
            print(f.read())

    # 清理临时文件
    import shutil
    shutil.rmtree(temp_dir)


if __name__ == "__main__":
    test_simple_logging()

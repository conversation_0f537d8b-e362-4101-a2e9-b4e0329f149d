# 日志文件
deploy_nuitka/
logs/
*/logs/
*.log
*.log.*
log_*.txt

# 数据文件
server/data/
server/data/*

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
deployment/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Python 缓存文件
*.cpython-*.pyc
*.cpython-*.pyo
*python-*.pyc
*python-*.pyo

# 虚拟环境
venv/
env/
ENV/

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# Docker 相关
.dockerignore

# 编译输出
*.o
*.obj
*.exe
*.dll
*.so
*.dylib

# Qt 相关
*.user
*.autosave
moc_*.cpp
moc_*.h
qrc_*.cpp
ui_*.h
*.Debug
*.Release
debug/
release/

